%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 468a46d0ae32c3544b7d98094e6448a9, type: 3}
  m_Name: AddressableAssetSettings
  m_EditorClassIdentifier: 
  m_DefaultGroup: ba1c91c9dc49045f99f7eb7c3f8e0b41
  m_currentHash:
    serializedVersion: 2
    Hash: 971374a455266bb5602acde0334230ca
  m_OptimizeCatalogSize: 0
  m_BuildRemoteCatalog: 0
  m_CatalogRequestsTimeout: 0
  m_DisableCatalogUpdateOnStart: 0
  m_InternalIdNamingMode: 0
  m_InternalBundleIdMode: 1
  m_AssetLoadMode: 0
  m_BundledAssetProviderType:
    m_AssemblyName: Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    m_ClassName: UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider
  m_AssetBundleProviderType:
    m_AssemblyName: Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    m_ClassName: UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider
  m_IgnoreUnsupportedFilesInBuild: 0
  m_UniqueBundleIds: 0
  m_EnableJsonCatalog: 0
  m_NonRecursiveBuilding: 1
  m_CCDEnabled: 0
  m_maxConcurrentWebRequests: 3
  m_UseUWRForLocalBundles: 0
  m_BundleTimeout: 0
  m_BundleRetryCount: 0
  m_BundleRedirectLimit: -1
  m_SharedBundleSettings: 0
  m_SharedBundleSettingsCustomGroupIndex: 0
  m_ContiguousBundles: 1
  m_StripUnityVersionFromBundleBuild: 0
  m_DisableVisibleSubAssetRepresentations: 0
  m_BuiltInBundleNaming: 0
  mBuiltInBundleCustomNaming: 
  m_MonoScriptBundleNaming: 0
  m_CheckForContentUpdateRestrictionsOption: 0
  m_MonoScriptBundleCustomNaming: 
  m_RemoteCatalogBuildPath:
    m_Id: 
  m_RemoteCatalogLoadPath:
    m_Id: 
  m_ContentStateBuildPathProfileVariableName: 
  m_CustomContentStateBuildPath: 
  m_ContentStateBuildPath: 
  m_BuildAddressablesWithPlayerBuild: 0
  m_overridePlayerVersion: '[UnityEditor.PlayerSettings.bundleVersion]'
  m_GroupAssets:
  - {fileID: 11400000, guid: 31062be85effe4a64bf653f4d0e72753, type: 2}
  - {fileID: 11400000, guid: 5b1bf7c9f89234c488ed2e69762260fa, type: 2}
  - {fileID: 11400000, guid: 61b58e3b3cee44131af182beea2e0171, type: 2}
  - {fileID: 11400000, guid: eb4aaaa28da6540c38a2ea2bc7da5a14, type: 2}
  - {fileID: 11400000, guid: 04a9bdace26b04e14af2512bc50fc68c, type: 2}
  - {fileID: 11400000, guid: eb0a58b3d1ef04a8682b63153d97f75c, type: 2}
  - {fileID: 11400000, guid: c84f93587e3ab495b945d7a09b1425f3, type: 2}
  - {fileID: 11400000, guid: 2fa9bdd24856e473faf0b21c41a09889, type: 2}
  - {fileID: 11400000, guid: e00a4403dcc0344cc840c7ff76a701de, type: 2}
  m_BuildSettings:
    m_LogResourceManagerExceptions: 1
    m_BundleBuildPath: Temp/com.unity.addressables/AssetBundles
  m_ProfileSettings:
    m_Profiles:
    - m_InheritedParent: 
      m_Id: 28f9571cf29f54349879620d549d0eda
      m_ProfileName: Default
      m_Values:
      - m_Id: 375331339d26848b0aa2cdc41abac164
        m_Value: '[UnityEditor.EditorUserBuildSettings.activeBuildTarget]'
      - m_Id: 4465c223ac26347eaa502484ee3c0b50
        m_Value: <undefined>
      - m_Id: 48d4de21c41f241a5a8ad764159a5ed6
        m_Value: '[UnityEngine.AddressableAssets.Addressables.BuildPath]/[BuildTarget]'
      - m_Id: 4d26730968d2f444db49ad48652102fb
        m_Value: '{UnityEngine.AddressableAssets.Addressables.RuntimePath}/[BuildTarget]'
      - m_Id: de5258434019d45328973076264909e1
        m_Value: 'ServerData/[BuildTarget]'
    m_ProfileEntryNames:
    - m_Id: 375331339d26848b0aa2cdc41abac164
      m_Name: BuildTarget
      m_InlineUsage: 0
    - m_Id: 4465c223ac26347eaa502484ee3c0b50
      m_Name: Remote.LoadPath
      m_InlineUsage: 0
    - m_Id: 48d4de21c41f241a5a8ad764159a5ed6
      m_Name: Local.BuildPath
      m_InlineUsage: 0
    - m_Id: 4d26730968d2f444db49ad48652102fb
      m_Name: Local.LoadPath
      m_InlineUsage: 0
    - m_Id: de5258434019d45328973076264909e1
      m_Name: Remote.BuildPath
      m_InlineUsage: 0
    m_ProfileVersion: 1
  m_LabelTable:
    m_LabelNames:
    - default
    - Locale
    - Locale-zh-CN
    - Locale-en-US
  m_SchemaTemplates: []
  m_GroupTemplateObjects:
  - {fileID: 11400000, guid: a1cc41efdb6724f7c969b579db0afed1, type: 2}
  m_InitializationObjects: []
  m_CertificateHandlerType:
    m_AssemblyName: 
    m_ClassName: 
  m_ActivePlayerDataBuilderIndex: 2
  m_DataBuilders:
  - {fileID: 11400000, guid: 61a2649e7c5f94ea4ae7f8f6833c3c49, type: 2}
  - {fileID: 11400000, guid: 753a2f3d418cd4dec9410fbfad44e920, type: 2}
  - {fileID: 11400000, guid: 8c1120cc7343b4deb9a0db5cbaa520cc, type: 2}
  m_ActiveProfileId: 28f9571cf29f54349879620d549d0eda
