%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a07b5cd0b1b829245bc8c4b6978793e8, type: 3}
  m_Name: Localization Settings
  m_EditorClassIdentifier: 
  m_StartupSelectors:
  - rid: 5464684008369553600
  - rid: 5464684008369553624
  - rid: 5464684008369553601
  - rid: 5464684008369553602
  m_AvailableLocales:
    rid: 5464684008369553603
  m_AssetDatabase:
    rid: 5464684008369553604
  m_StringDatabase:
    rid: 5464684008369553605
  m_Metadata:
    m_Items: []
  m_ProjectLocaleIdentifier:
    m_Code: en-US
  m_PreloadBehavior: 1
  m_InitializeSynchronously: 0
  references:
    version: 2
    RefIds:
    - rid: -2
      type: {class: , ns: , asm: }
    - rid: 5464684008369553600
      type: {class: CommandLineLocaleSelector, ns: UnityEngine.Localization.Settings,
        asm: Unity.Localization}
      data:
        m_CommandLineArgument: -language=
    - rid: 5464684008369553601
      type: {class: SystemLocaleSelector, ns: UnityEngine.Localization.Settings, asm: Unity.Localization}
      data: 
    - rid: 5464684008369553602
      type: {class: SpecificLocaleSelector, ns: UnityEngine.Localization.Settings,
        asm: Unity.Localization}
      data:
        m_LocaleId:
          m_Code: en-US
    - rid: 5464684008369553603
      type: {class: LocalesProvider, ns: UnityEngine.Localization.Settings, asm: Unity.Localization}
      data: 
    - rid: 5464684008369553604
      type: {class: LocalizedAssetDatabase, ns: UnityEngine.Localization.Settings,
        asm: Unity.Localization}
      data:
        m_DefaultTableReference:
          m_TableCollectionName: 
        m_CustomTableProvider:
          rid: -2
        m_CustomTablePostprocessor:
          rid: -2
        m_AsynchronousBehaviour: 0
        m_UseFallback: 0
    - rid: 5464684008369553605
      type: {class: LocalizedStringDatabase, ns: UnityEngine.Localization.Settings,
        asm: Unity.Localization}
      data:
        m_DefaultTableReference:
          m_TableCollectionName: 
        m_CustomTableProvider:
          rid: -2
        m_CustomTablePostprocessor:
          rid: -2
        m_AsynchronousBehaviour: 0
        m_UseFallback: 0
        m_MissingTranslationState: 1
        m_NoTranslationFoundMessage: No translation found for '{key}' in {table.TableCollectionName}
        m_SmartFormat:
          rid: 5464684008369553606
    - rid: 5464684008369553606
      type: {class: SmartFormatter, ns: UnityEngine.Localization.SmartFormat, asm: Unity.Localization}
      data:
        m_Settings:
          rid: 5464684008369553607
        m_Parser:
          rid: 5464684008369553608
        m_Sources:
        - rid: 5464684008369553609
        - rid: 5464684008369553610
        - rid: 5464684008369553611
        - rid: 5464684008369553612
        - rid: 5464684008369553613
        - rid: 5464684008369553614
        - rid: 5464684008369553615
        m_Formatters:
        - rid: 5464684008369553609
        - rid: 5464684008369553616
        - rid: 5464684008369553617
        - rid: 5464684008369553618
        - rid: 5464684008369553619
        - rid: 5464684008369553620
        - rid: 5464684008369553621
        - rid: 5464684008369553622
        - rid: 5464684008369553623
    - rid: 5464684008369553607
      type: {class: SmartSettings, ns: UnityEngine.Localization.SmartFormat.Core.Settings,
        asm: Unity.Localization}
      data:
        m_FormatErrorAction: 0
        m_ParseErrorAction: 0
        m_CaseSensitivity: 0
        m_ConvertCharacterStringLiterals: 1
    - rid: 5464684008369553608
      type: {class: Parser, ns: UnityEngine.Localization.SmartFormat.Core.Parsing,
        asm: Unity.Localization}
      data:
        m_OpeningBrace: 123
        m_ClosingBrace: 125
        m_Settings:
          rid: 5464684008369553607
        m_AlphanumericSelectors: 1
        m_AllowedSelectorChars: _-
        m_Operators: '[]().,'
        m_AlternativeEscaping: 0
        m_AlternativeEscapeChar: 92
    - rid: 5464684008369553609
      type: {class: ListFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - list
        - l
        - 
        m_SmartSettings:
          rid: 5464684008369553607
    - rid: 5464684008369553610
      type: {class: PersistentVariablesSource, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Groups: []
    - rid: 5464684008369553611
      type: {class: DictionarySource, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data: 
    - rid: 5464684008369553612
      type: {class: ValueTupleSource, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data: 
    - rid: 5464684008369553613
      type: {class: XmlSource, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data: 
    - rid: 5464684008369553614
      type: {class: ReflectionSource, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data: 
    - rid: 5464684008369553615
      type: {class: DefaultSource, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data: 
    - rid: 5464684008369553616
      type: {class: PluralLocalizationFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - plural
        - p
        - 
        m_DefaultTwoLetterISOLanguageName: en
    - rid: 5464684008369553617
      type: {class: ConditionalFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - conditional
        - cond
        - 
    - rid: 5464684008369553618
      type: {class: TimeFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - timespan
        - time
        - t
        - 
        m_DefaultFormatOptions: 4646
    - rid: 5464684008369553619
      type: {class: XElementFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - xelement
        - xml
        - x
        - 
    - rid: 5464684008369553620
      type: {class: ChooseFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - choose
        - c
        m_SplitChar: 124
    - rid: 5464684008369553621
      type: {class: SubStringFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - substr
        m_ParameterDelimiter: 44
        m_NullDisplayString: (null)
        m_OutOfRangeBehavior: 0
    - rid: 5464684008369553622
      type: {class: IsMatchFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - ismatch
    - rid: 5464684008369553623
      type: {class: DefaultFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - default
        - d
        - 
    - rid: 5464684008369553624
      type: {class: PlayerPrefLocaleSelector, ns: UnityEngine.Localization.Settings,
        asm: Unity.Localization}
      data:
        m_PlayerPreferenceKey: selected-locale
